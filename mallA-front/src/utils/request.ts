import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { Session } from './storage'
import qs from 'qs'
import { useMessageBox } from '../hooks/message'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus';
// import { useMessageBox } from '../hooks/message';
/**
 * 创建并配置一个 Axios 实例对象
 */
// 根据环境设置 baseURL
const getBaseURL = () => {
  // 检查是否在预览模式下（通过端口号判断）
  const isPreview = window.location.port === '2030';

  if (import.meta.env.MODE === 'production' && !isPreview) {
    // 真正的生产环境使用相对路径，让浏览器自动使用当前域名
    return '';
  }
  return '/api';
};

const service: AxiosInstance = axios.create({
  baseURL: getBaseURL(),
  timeout: 50000, // 全局超时时间
  paramsSerializer: {
    serialize: (params: any) => {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  }
})

/**
 * Axios请求拦截器，对请求进行处理
 * 1. 序列化get请求参数
 * 2. 统一增加Authorization和TENANT-ID请求头
 * 3. 自动适配单体、微服务架构不同的URL
 * @param config AxiosRequestConfig对象，包含请求配置信息
 */
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 统一增加Authorization请求头, skipToken 跳过增加token
    const token = Session.getToken()
    config.headers!['Content-Type'] = 'application/json; charset=UTF-8'
    if (token && !config.headers?.skipToken) {
        config.headers![CommonHeaderEnum.AUTHORIZATION] = `Bearer ${token}`
    }
    // 处理完毕，返回config对象
    return config
  },
  (error) => {
    const router = useRouter()
    if (error.response && error.response.status === 403) {
      console.warn('Token失效，跳转登录页')
      localStorage.removeItem('token')
      router.push('/')
    } else {
      console.error('请求错误', error)
    }
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器处理函数
 * @param response 响应结果
 * @returns 如果响应成功，则返回响应的data属性；否则，抛出错误或者执行其他操作
 */
const handleResponse = (response: AxiosResponse<any>) => {
  // 如果是 blob 类型响应（文件下载），直接返回 data
  if (response.config.responseType === 'blob') {
    return response.data;
  }

  if (response.data.code === 500) {
    throw response.data
  }

  // 针对密文返回解密
  // if (response.data.encryption) {
  // 	const originData = JSON.parse(other.decryption(response.data.encryption, import.meta.env.VITE_PWD_ENC_KEY));
  // 	response.data = originData;
  // 	return response.data;
  // }

  return response.data
}

/**
 * 添加 Axios 的响应拦截器，用于全局响应结果处理
 */

let isRefreshing = false; // 控制是否已弹框或正在处理 token 失效

service.interceptors.response.use(
  handleResponse,
  (error) => {
    const status = error.response?.status;

    if (status === 401 || status === 403 || status === 424) {
      if (!isRefreshing) {
        isRefreshing = true;

		ElMessageBox
		.confirm('检测到您的账号在其他地方登录。如非本人操作，请立即【修改密码】', '提示', {
		  confirmButtonText: '确定',
		  cancelButtonText: '取消',
		  type: 'warning',
		//   closeOnClickModal: false, // 防止点击遮罩关闭
		//   closeOnPressEscape: false, // 防止按 ESC 关闭
		//   draggable: false,
		})
		.then(() => {
		  Session.clear();
		  window.location.href = '/login';
		})
		.catch(() => {
		  // 用户取消操作
		  
		})
		.finally(() => {
		  isRefreshing = false;
		});	
      }

      return Promise.reject(new Error('Token expired'));
    }

    return Promise.reject(error.response?.data || error);
  }
);

//常用header
export enum CommonHeaderEnum {
  'TENANT_ID' = 'TENANT-ID',
  'ENC_FLAG' = 'Enc-Flag',
  'AUTHORIZATION' = 'Authorization'
}

// 导出 axios 实例
export default service
